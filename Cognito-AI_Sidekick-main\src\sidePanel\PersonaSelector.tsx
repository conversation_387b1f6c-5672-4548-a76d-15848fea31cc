import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { useConfig } from './ConfigContext';
import { cn } from "@/src/background/util";
import { personaImages } from './constants';

export const PersonaSelector = () => {
  const { config, updateConfig } = useConfig();
  const currentPersona = config?.persona || 'default';
  const personas = config?.personas || {};
  const personaImageSrc = personaImages[currentPersona] || personaImages.default;

  const handlePersonaChange = (value: string) => {
    updateConfig({ persona: value });
  };

  return (
    <div className="flex items-center gap-3">
      <Avatar className="h-8 w-8 border border-border">
        <AvatarImage src={personaImageSrc} alt={currentPersona} />
        <AvatarFallback className="text-apple-footnote font-medium">
          {(currentPersona === 'default' ? 'C' : currentPersona.substring(0, 1)).toUpperCase()}
        </AvatarFallback>
      </Avatar>
      
      <Select value={currentPersona} onValueChange={handlePersonaChange}>
        <SelectTrigger className={cn(
          "w-auto min-w-[120px] border-none bg-transparent shadow-none",
          "text-apple-callout font-medium text-foreground",
          "hover:bg-secondary/50 rounded-lg px-3 py-1 h-8",
          "focus:ring-2 focus:ring-primary/20 focus:border-primary/50"
        )}>
          <SelectValue placeholder="Select persona" />
        </SelectTrigger>
        <SelectContent className="bg-popover border border-border shadow-lg rounded-lg">
          {Object.keys(personas).map((persona) => (
            <SelectItem
              key={persona}
              value={persona}
              className={cn(
                "text-apple-callout text-popover-foreground",
                "hover:bg-accent hover:text-accent-foreground",
                "focus:bg-accent focus:text-accent-foreground",
                "cursor-pointer rounded-md"
              )}
            >
              <div className="flex items-center gap-2">
                <Avatar className="h-5 w-5">
                  <AvatarImage src={personaImages[persona] || personaImages.default} alt={persona} />
                  <AvatarFallback className="text-xs">
                    {(persona === 'default' ? 'C' : persona.substring(0, 1)).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                {persona === 'default' ? 'Chromepanion' : persona}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};
