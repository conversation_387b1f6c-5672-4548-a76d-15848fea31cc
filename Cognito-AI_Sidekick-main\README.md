![](docs/banner.png)
# ChromePanion: Your AI Companion for Chrome 🚀

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![GitHub version](https://img.shields.io/github/v/release/user/ChromePanion)](https://github.com/user/ChromePanion/releases/latest)

**Chromepan<PERSON> is a private, local AI-powered web search assistant that brings intelligent search capabilities directly to your browser. Similar to Microsoft Edge's Copilot extension but with complete privacy, Chromepanion connects exclusively to local AI models (Ollama) to analyze and distill web search results through specialized AI personas.**

<!-- Optional: Add a slightly larger, more engaging screenshot or GIF here if available. docs/screenshot.png is good. -->
![](docs/web.gif) ![](docs/local.gif) 


## ✨ Core Features

*   **Private AI-Powered Web Search:**
    *   **Local Processing Only:** All AI analysis happens locally via Ollama - no external AI services
    *   **Google Search Integration:** Performs web searches and fetches content from top results
    *   **Intelligent Analysis:** AI analyzes and synthesizes search results based on your selected persona
*   **10 Specialized Search Personas:**
    *   **The Scholar:** Academic-style analysis with citations and source credibility assessment
    *   **The Executive:** Strategic business insights with actionable recommendations
    *   **The Storyteller:** Engaging narrative presentation of findings
    *   **The Skeptic:** Critical analysis highlighting biases and contradictions
    *   **The Mentor:** Educational explanations with learning guidance
    *   **The Investigator:** Methodical fact-checking and source verification
    *   **The Pragmatist:** Solution-focused practical applications
    *   **The Enthusiast:** Exciting discoveries and breakthrough insights
    *   **The Curator:** Sophisticated synthesis of premium insights
    *   **The Friend:** Casual, conversational sharing of interesting findings

*   **Productivity Boosters:**
    *   **Note Taking:** Save context, chat snippets, and important information directly within the side panel.

    *   **Chat History:** Keep track of your interactions.
*   **Developer-Friendly:** Built with a modern tech stack, open-source, and ready for contributions.

## 🛠️ How It Works

ChromePanion is a Chrome extension built with a modular architecture:

*   **Side Panel (React & Redux):** The main user interface where you interact with the AI, manage settings, and view results. Built with React for a dynamic experience and Redux (via `webext-redux`) for robust state management.
*   **Background Script:** The engine of the extension. It handles communication with AI services, manages long-running tasks, injects content scripts, and coordinates actions across the extension.
*   **Content Scripts:** Injected into web pages to securely access and relay page content (text, HTML) to the Side Panel and Background Script for processing by the AI.

This setup allows ChromePanion to understand the context of your browsing and provide relevant AI assistance without leaving your current tab.

## 💻 Technology Stack

*   **React:** For building the interactive Side Panel UI.
*   **TypeScript:** For robust and maintainable code.
*   **Redux & `webext-redux`:** For state management across the extension components.
*   **Tailwind CSS:** For styling the user interface.
*   **Webpack:** For bundling the extension.
*   Various UI libraries (Radix UI components like `@radix-ui/react-accordion`, `lucide-react` for icons) for a polished look and feel.

## 🚀 Getting Started

### Prerequisites

*   Google Chrome

### Installation

#### Option 1: From Chrome Web Store (Recommended for most users)
*   Install from the [Chrome Web Store](https://chromewebstore.google.com/detail/pphjdjdoclkedgiaahmiahladgcpohca?utm_source=item-share-cb).

#### Option 2: From Release (Manual Install)
1.  Download the latest file from the [Releases page](https://github.com/user/ChromePanion/releases).
2.  Extract the downloaded ZIP file to a permanent folder on your computer.
3.  Open Chrome and navigate to `chrome://extensions`.
4.  Enable **Developer mode** using the toggle in the top-right corner.
5.  Click the **Load unpacked** button.
6.  Select the folder where you extracted the ZIP file.

#### Option 3: From Source (For Developers)
1.  Clone the repository:
    ```bash
    git clone https://github.com/user/ChromePanion.git
    cd ChromePanion
    ```
2.  Install dependencies:
    ```bash
    npm install
    ```
3.  Build the extension:
    ```bash
    npm start
    ```
    This will generate the bundled extension in the `dist/chrome` folder.
4.  Open Chrome and navigate to `chrome://extensions`.
5.  Enable **Developer mode**.
6.  Click **Load unpacked** and select the `dist/chrome` folder.

## 📖 Usage Examples

*   **Academic Research:** Select "The Scholar" persona and search "latest developments in quantum computing applications" to get academic-style analysis with proper citations and source credibility assessment.
*   **Business Intelligence:** Use "The Executive" persona to search "market trends in electric vehicles 2024" for strategic insights with actionable business recommendations.
*   **Fact Checking:** Choose "The Investigator" persona to search "climate change statistics 2024" for methodical verification of claims and source reliability assessment.
*   **Learning New Topics:** Select "The Mentor" persona and search "how blockchain technology works" for educational explanations with step-by-step guidance.
*   **Creative Exploration:** Use "The Storyteller" persona to search "history of space exploration" for engaging narrative presentation of findings.
*   **Connect to Local LLM:** Ensure Ollama is running locally (e.g., `http://localhost:11434`), go to Chromepanion's settings, connect to Ollama, and start searching with complete privacy.

## ⚙️ Configuration

*   **Connecting to Ollama:** Access the settings panel to configure connections to your local Ollama instance. You'll need to have Ollama running locally with your preferred models (e.g., `http://localhost:11434`).
*   **Choosing Search Personas:** Select from 10 specialized search personas to tailor how AI analyzes and presents your search results:
    *   **Scholar, Executive, Storyteller, Skeptic, Mentor, Investigator, Pragmatist, Enthusiast, Curator, Friend**
*   **Privacy First:** All AI processing happens locally through Ollama - no data is sent to external AI services, ensuring complete privacy for your searches.


## 🗺️ Roadmap

*   Ongoing bug fixes and performance improvements.
*   Evaluation and integration of community pull requests.
*   **Enhanced Agent Capabilities:**
    *   "Memory" for chat history with RAG (Retrieval Augmented Generation) and semantic search.
    *   Autonomously invoke internal tools (like “save note”, “search note”, “summarize page”) without switching modes. Here’s how to pull it off: Adding a small tool-invoking agent layer; Capturing tool-friendly intent (few-shot or system prompt); Internally calling functions when confidence is high. [^1]
    *   Better websearch with [Deepsearch](https://github.com/google-gemini/gemini-fullstack-langgraph-quickstart)
    *   "Short-term Memory" (state management) for multi-step tasks within the same context (e.g., web search followed by page parsing and comparison). Note would be used for this.
    *   Direct text editing/interaction on web pages via the side panel – extending Cognito towards an "AI agent" experience.

*   Potential support for image and voice API interactions for multimodal capabilities.
*   ~Change notes to link + hover card, add tags, change the dustbin to ...+dropdownmenu/context menu/menu~
*   A hybrid RAG system starting with BM25 is smart for speed and local search. [wink-bm25-text-search](https://github.com/winkjs/wink-bm25-text-search) – fast, no dependencies, lightweight

```
const bm25 = require('wink-bm25-text-search')();
bm25.defineConfig({ fldWeights: { title: 1, content: 2 } });

bm25.definePrepTasks([
  // optional: tokenize, lowercase, remove stopwords
]);

// Add documents (your notes)
bm25.addDoc({ title: "Page summary", content: "..." }, docId);

// Search
const results = bm25.search("keyword or phrase");
```

[^1]: 
```
const userPrompt = "Summarize this page and save to my notes";

const tools = [
  { name: "saveNote", description: "Save text content to note system" },
  ...
];

const res = await openai.chat.completions.create({
  model: "gpt-4",
  messages: [...],
  tools,
  tool_choice: "auto"
});
```

*(This section will be regularly updated based on project progress)*

## 🤝 Contributing

Contributions are welcome! If you'd like to help improve ChromePanion, please:

1.  Fork the repository.
2.  Create a new branch for your feature or bug fix: `git checkout -b feature/your-feature-name` or `bugfix/issue-number`.
3.  Make your changes.
4.  Ensure your code lints (e.g., `npm run lint` if a lint script is configured) and builds correctly (`npm start`).
5.  Submit a pull request with a clear description of your changes.

*(Consider adding details on coding style, development setup, or linking to a dedicated CONTRIBUTING.md file if one is created in the future.)*

## 🙏 Acknowledgments

*   ChromePanion was originally built upon and inspired by [sidellama](https://github.com/gyopak/sidellama).
*   Inspiration and ideas from projects like Stanford's [WikiChat](https://github.com/stanford-oval/WikiChat), [highCompute.py](https://github.com/AlexBefest/highCompute.py) by AlexBefest, [StreamingKokoroJS](https://github.com/rhulha/StreamingKokoroJS), [WebAgent](https://github.com/Alibaba-NLP/WebAgent), [chatterbox](https://github.com/resemble-ai/chatterbox), [kokoro and kokoro.js](https://github.com/hexgrad/kokoro/tree/main/kokoro.js) and the [piper-browser-extension](https://github.com/ken107/piper-browser-extension).
*   Thanks to all the developers of the open-source libraries and tools that make ChromePanion possible.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
